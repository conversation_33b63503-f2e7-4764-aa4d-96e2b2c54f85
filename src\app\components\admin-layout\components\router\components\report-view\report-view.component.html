<div class="main-content fixed-height">
  <div class="container-fluid">
    <div class="content">
      <div class="row">
        <div class="col-md-12 card report-content">
          <div class="card list-header" style="height: auto; padding-bottom: 15px;">
            <div class="header">
              <button class="{{activeTab === 'unlock' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                (click)="switchToTab('unlock')">
                Unlocks
              </button>
              <button class="{{activeTab === 'battle' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                (click)="switchToTab('battle')" style="margin-left: 5px;">
                Battles
              </button>
              <button class="{{activeTab === 'class' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                (click)="switchToTab('class')" style="margin-left: 5px;">
                Classes
              </button>
              <button class="{{activeTab === 'message' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                (click)="switchToTab('message')" style="margin-left: 5px;">
                Messages (Omit & Introduction)
              </button>
              <button class="{{activeTab === 'weight' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                (click)="switchToTab('weight')" style="margin-left: 5px;">
                Message (Weight)
              </button>
              <button class="{{activeTab === 'special-item' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                (click)="switchToTab('special-item')" style="margin-left: 5px;">
                Special Item
              </button>
              <button class="{{activeTab === 'currency' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                (click)="switchToTab('currency')" style="margin-left: 5px;">
                Currency
              </button>
              <button class="{{activeTab === 'general-items' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                (click)="switchToTab('general-items')" style="margin-left: 5px;">
                General Items
              </button>
              <button
                class="{{activeTab === 'finish-dialogue-marker' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                (click)="switchToTab('finish-dialogue-marker')" style="margin-left: 5px;">
                Finish Dialogue Markers
              </button>
              <button class="{{activeTab === 'pin-marker' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                (click)="switchToTab('pin-marker')" style="margin-left: 5px;">
                Pin Markers
              </button>
              <button class="{{activeTab === 'restart' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                (click)="switchToTab('restart')" style="margin-left: 5px;">
                Restart Dialog
              </button>
              <button class="{{activeTab === 'progress' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                (click)="switchToTab('progress')" style="margin-left: 5px;">
                Progress Conditions
              </button>
              <button class="{{activeTab === 'collectible' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                (click)="switchToTab('collectible')" style="margin-left: 5px;">
                Marked as Collectible
              </button>
              <button class="{{activeTab === 'choice' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                (click)="switchToTab('choice')" style="margin-left: 5px;">
                Choice
              </button>
              <button class="{{activeTab === 'dilemma' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                (click)="switchToTab('dilemma')" style="margin-left: 30px;">
                Dilemma Points
              </button>
              <button class="{{activeTab === 'difficulty' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
                (click)="switchToTab('difficulty')" style="margin-left: 5px;">
                Difficulty Class (DC) - Attribute
              </button>
              <button class="{{activeTab === 'difficulty-knowledge' ? 'btn btn-fill selectedButton' : 'btn btn-fill'}}"
              (click)="switchToTab('difficulty-knowledge')" style="margin-left: 5px;">
               Difficulty Class (DC) - Knowledge
            </button>
            </div>
          </div>
          <app-level-report *ngIf="activeTab === 'unlock'"> </app-level-report>
          <app-battle-report *ngIf="activeTab === 'battle'"></app-battle-report>
          <app-class-report *ngIf="activeTab === 'class'"> </app-class-report>
          <app-message-report *ngIf="activeTab === 'message'"> </app-message-report>
          <app-weight-report *ngIf="activeTab === 'weight'"> </app-weight-report>
          <app-special-item-report *ngIf="activeTab === 'special-item'"> </app-special-item-report>
          <app-currency-report *ngIf="activeTab === 'currency'"> </app-currency-report>
          <app-level-dialogue-report *ngIf="activeTab === 'finish-dialogue-marker'"> </app-level-dialogue-report>
          <app-level-pin-report *ngIf="activeTab === 'pin-marker'"> </app-level-pin-report>
          <app-restart-dialogue-report *ngIf="activeTab === 'restart'"></app-restart-dialogue-report>
          <app-progress-condition-report *ngIf="activeTab === 'progress'"></app-progress-condition-report>
          <app-marked-collectible *ngIf="activeTab === 'collectible'"></app-marked-collectible>
          <app-choice-cadence-report *ngIf="activeTab === 'choice'"></app-choice-cadence-report>
          <app-general-items *ngIf="activeTab === 'general-items'"></app-general-items>
          <app-dilemma-report *ngIf="activeTab === 'dilemma'"></app-dilemma-report>
          <app-difficulty-class-reports *ngIf="activeTab === 'difficulty'"></app-difficulty-class-reports>
          <app-knowledge-difficult-class *ngIf="activeTab === 'difficulty-knowledge'"></app-knowledge-difficult-class>
        </div>
      </div>
    </div>
  </div>
</div>