import { Component, OnInit } from '@angular/core';
import { FILTER_SUFFIX_PATH } from 'src/lib/darkcloud/angular/dsadmin/constants/others';
import { ItemClassService } from 'src/app/services/item-class.service';
import { CustomService } from 'src/app/services/custom.service';
import { Custom } from 'src/app/lib/@bus-tier/models/Custom';

@Component({
  selector: 'app-weapons-wlbase-generator',
  templateUrl: './weapons-wlbase-generator.component.html',
  styleUrls: ['./weapons-wlbase-generator.component.scss']
})
export class WeaponsWLBaseGeneratorComponent implements OnInit
{
  public activeTab: string;
  public weaponType: string = 'common';
  public subActiveTab: string = 'item-class';
  public custom: Custom;

  constructor(
    private _itemClassService: ItemClassService,
    private _customService: CustomService
  ) {}

  async ngOnInit(): Promise<void> {
    const tab = localStorage.getItem(
      `tab-WeaponsWLBaseGeneratorComponent${FILTER_SUFFIX_PATH}`
    );
    this.activeTab = tab === 'null' || !tab ? 'class-selection' : tab;

    // Load weapon type and sub tab states
    const weaponType = localStorage.getItem(
      `weaponType-WeaponsWLBaseGeneratorComponent${FILTER_SUFFIX_PATH}`
    );
    this.weaponType = weaponType === 'null' || !weaponType ? 'common' : weaponType;

    const subTab = localStorage.getItem(
      `subTab-WeaponsWLBaseGeneratorComponent${FILTER_SUFFIX_PATH}`
    );
    this.subActiveTab = subTab === 'null' || !subTab ? 'item-class' : subTab;

    await this._itemClassService.toFinishLoading();
    await this._customService.toFinishLoading();
    this.custom = await this._customService.svcGetInstance();

    // Auto-select item classes based on active tab and weapon type
    await this.autoSelectItemClasses();
    await this.autoSelectBasedOnWeaponType();
  }

  public async switchToTab(tab: string) {
    this.activeTab = tab;
    localStorage.setItem(
      `tab-WeaponsWLBaseGeneratorComponent${FILTER_SUFFIX_PATH}`,
      this.activeTab
    );

    // Auto-select item classes when switching tabs
    await this.autoSelectItemClasses();
  }

  public async switchToWeaponType(type: string) {
    this.weaponType = type;
    localStorage.setItem(
      `weaponType-WeaponsWLBaseGeneratorComponent${FILTER_SUFFIX_PATH}`,
      this.weaponType
    );

    // Auto-select item classes based on weapon type
    await this.autoSelectBasedOnWeaponType();
  }

  public switchToSubTab(tab: string) {
    this.subActiveTab = tab;
    localStorage.setItem(
      `subTab-WeaponsWLBaseGeneratorComponent${FILTER_SUFFIX_PATH}`,
      this.subActiveTab
    );
  }

  private async autoSelectItemClasses() {
    if (this.activeTab === 'common-weapons') {
      await this.autoSelectCommonWeapons();
    } else if (this.activeTab === 'special-weapons') {
      await this.autoSelectSpecialWeapons();
    }
  }

  private async autoSelectBasedOnWeaponType() {
    if (this.weaponType === 'common') {
      await this.autoSelectCommonWeapons();
    } else if (this.weaponType === 'special') {
      await this.autoSelectSpecialWeapons();
    }
  }

  private async autoSelectCommonWeapons() {
    if (!this.custom.weaponsWLBaseClassItem) {
      this.custom.weaponsWLBaseClassItem = [];
    }

    // Find "ARMAS COMUNS" class
    const armasComunsClass = this._itemClassService.models.find((x) => x.name === "ARMAS COMUNS");

    if (armasComunsClass) {
      // Clear existing selections and set only ARMAS COMUNS
      this.custom.weaponsWLBaseClassItem = [armasComunsClass.id];
      this._customService.svcToModify(this.custom);
      this._customService.toSave();
    }
  }

  private async autoSelectSpecialWeapons() {
    if (!this.custom.weaponsWLBaseClassItem) {
      this.custom.weaponsWLBaseClassItem = [];
    }

    // Find "BLUEPRINTS" and "ARMAS ESPECIAIS" classes
    const blueprintsClass = this._itemClassService.models.find((x) => x.name === "BLUEPRINTS");
    const armasEspeciaisClass = this._itemClassService.models.find((x) => x.name === "ARMAS ESPECIAIS");

    const selectedClasses = [];
    if (blueprintsClass) {
      selectedClasses.push(blueprintsClass.id);
    }
    if (armasEspeciaisClass) {
      selectedClasses.push(armasEspeciaisClass.id);
    }

    if (selectedClasses.length > 0) {
      // Clear existing selections and set BLUEPRINTS and ARMAS ESPECIAIS
      this.custom.weaponsWLBaseClassItem = selectedClasses;
      this._customService.svcToModify(this.custom);
      this._customService.toSave();
    }
  }
}
