
.content-weapon
{
 // margin: 25px;
  margin-top: 10px;
  margin-left: 25px;
  margin-right: 25px;
}

.iconInter {
  text-align: center; 
  font-size: 30px !important; 
  margin-top: 8px;
}
i:hover{
  color: red;
  cursor: pointer;
}

.justPass {
  position: relative;
  top: -37px;
  width: 30%;
  float: right;
}

/* Fundo escurecido (backdrop) */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5); /* Ajuste a opacidade aqui */
  z-index: 998; /* Deve ser menor que o modal */
}


/*Modal do sistema */
.item-overlay
{
    position: absolute;
    //border: 15px solid black;
    height: 100%;
    width: 100%;
    background-color: rgb(230, 230, 230);
    z-index: 150;
}

.popup-report
{ 
  border-radius: 8px;
  width: 480px;
  position: fixed;
 // left: 35%;
  padding: 24px;
  top: 23%;
 // transform: translate(-60%, -50%);
 transform: translate(-10%, -40%);
  z-index: 1000;
  opacity: 1;  

}
.modal-header {
  color: white !important;
  padding: 0px !important;

  .modal-title {
    text-align: left !important;
    margin-bottom: 7px;
    .close {
      opacity: 0px !important;
      margin-top: 0px !important;
    }
  }

   button {
    span {
      color: white !important;
    }
   }
}

.close {
  opacity: 1 !important;
 }

 .contextInfo {
  color: white;
  text-align: left;
  overflow-y: auto;
  white-space: pre-wrap;
  height: auto;
  max-height: 700px;
  scrollbar-width: thin;
  scroll-behavior: auto;  
  scrollbar-color: white black;
  padding-top: 10px;
}
.background-div {	
  position: relative;
  display: flex;
  justify-content: center;
  z-index: 9999;
}	

.background-div.popup-open:before 	
{	
  content: "";	
  position: fixed;	
  top: 0;	
  left: 0;	
  width: 100%;	
  height: 100%;	
  background-color: rgba(0, 0, 0, 0.5);	
  z-index: 9998;	
  pointer-events: none;	
}	

// FIM DO MODAL
