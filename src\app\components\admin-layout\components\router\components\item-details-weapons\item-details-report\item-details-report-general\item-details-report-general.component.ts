import { AfterViewInit, Component, Input, OnInit, ViewChild } from '@angular/core';
import { Effect, Item, Weapon } from 'src/app/lib/@bus-tier/models';
import { Custom } from 'src/app/lib/@bus-tier/models/Custom';
import { EffectService, ItemService, WeaponService } from 'src/app/services';
import { CustomService } from 'src/app/services/custom.service';
import { LanguageService } from 'src/app/services/language.service';
import { ICollectibleDetails } from 'src/lib/darkcloud/angular/dsadmin/v9/data/result';
import { CharacterSelector } from '../../../selectors/character-selector/character-selector.component';
import { ClassSelector } from '../../../selectors/class-selector/class-selector.component';
import { EnabledClassSelector } from '../../../selectors/enabled-class-selector/enabled-class-selector.component';
import { EnabledCharacterSelector } from '../../../selectors/enabled-character-selector/enabled-character-selector.component';
import { CommonWeaponsService } from 'src/app/services/commonWeaponsService';

@Component({
  selector: 'app-item-details-report-general',
  templateUrl: './item-details-report-general.component.html',
  styleUrls: ['./item-details-report-general.component.scss'],
})
export class ItemDetailsReportGeneralComponent implements OnInit, ICollectibleDetails, AfterViewInit 
{
  @ViewChild(CharacterSelector) _characterSelector:CharacterSelector;
  @ViewChild(EnabledCharacterSelector) _enabledCharacterSelector:EnabledCharacterSelector;
  @ViewChild(ClassSelector) _classSelector:ClassSelector;
  @ViewChild(EnabledClassSelector) _enabledClassSelector:EnabledClassSelector;

  @Input() weaponId = '';
  lstLanguage: string;
  custom: Custom;
  item: Item;
  weapon: Weapon;
  effects: Effect[];

  constructor(
    protected _customService: CustomService,
    protected _languageService: LanguageService,
    private _itemService: ItemService,
    private _weaponService: WeaponService,
    private _effectService: EffectService,
    private _commonWeaponsService: CommonWeaponsService,
  ) {}

  public async ngOnInit(): Promise<void> 
  {
    await this._weaponService.toFinishLoading();
    await this._effectService.toFinishLoading();
    await this._itemService.toFinishLoading();

    this.verifyIfWeaponHasCurrentItem();
    this.effects = this._effectService.models;
    this.lstLanguage = this._languageService.activeLanguage.name; 
    this.AddDataCommonWeapons(); 
  }

  AddDataCommonWeapons() {
    const commons = this._commonWeaponsService.models;  

    for (let index = 0; index < commons.length; index++) {
      commons[index].commonWeaponReceivedHC.forEach((x) => {  
          if(x.idNameHC === this.weapon.itemId) {
            this.weapon.wlbase =  x.wlBase;
            this.weapon.qiMin = x.qi_Min;
            this.weapon.luckMin =  x.luck_min;
            this._weaponService.svcToModify(this.weapon);
          }         
      });      
    }
  }
  
  async ngAfterViewInit()
  {
    await this.getCurrentWeaponBetweenTabs();
  }

  async getCurrentWeaponBetweenTabs()
  {
    this.weaponId = await this._customService.currentSelectedItem;
    this.setCurrentWeapon();
    this.reset(this.weaponId);
  }

  verifyIfWeaponHasCurrentItem()
  {
    let hasWeapon = false;
    for(let i = 0; i < this._weaponService.models.length; i++)
    {
      if(this._weaponService.models[i].itemId == this._weaponService.currentSelectedCharacter)
      {
        this.weapon = this._weaponService.models[i];
        hasWeapon = true;
        break;
      }
    }
    this.insertCurrentItemIntoWeaponArray(hasWeapon);
  }

  async insertCurrentItemIntoWeaponArray(hasWeapon:boolean)
  {
    if(!hasWeapon)
    {
      this.weapon = this._weaponService.createNewWeapon(this._weaponService.currentSelectedCharacter);
    }
  }

  async reset(weaponId) 
  {
    this.weaponId = weaponId;
    this.weapon = this._weaponService.models.find((w) => w.itemId === this.weaponId);

    this._classSelector.reset(this.weapon?.classesId, this.weapon?.enabledClassesId)
    this._characterSelector.reset(this.weapon?.charactersId, this.weapon?.enabledCharactersId);

    this._enabledClassSelector.reset(this.weapon?.enabledClassesId, this.weapon?.classesId)
    this._enabledCharacterSelector.reset(this.weapon?.enabledCharactersId, this.weapon?.charactersId);
    this.ngOnInit();  
  }

  setCurrentWeapon()
  {
    for(let i = 0; i < this._weaponService.models.length; i++)
    {
      if(this._weaponService.models[i].itemId == this.weaponId)
      {
        this.weapon = this._weaponService.models[i];
      }
    }
  }

  public async changeItemValue(itemField:string, value:string)
  {
    this.weapon[itemField] = value;
    await this._weaponService.svcToModify(this.weapon);
    await this._weaponService.toSave();
    this.getCurrentWeaponBetweenTabs();
  }

  async changeSelectedId(ids: string[], fieldName:string) 
  {
    this.weapon[fieldName] = ids;
    await this._weaponService.svcToModify(this.weapon);
    await this._weaponService.toSave();
  } 
}
