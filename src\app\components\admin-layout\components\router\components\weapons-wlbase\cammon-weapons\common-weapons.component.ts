import { ChangeDetectorRef, Component } from '@angular/core';
import { Area } from 'src/app/lib/@bus-tier/models';
import { CommonWeapons } from 'src/app/lib/@bus-tier/models/CommonWeapons';
import { ItemClass } from 'src/app/lib/@bus-tier/models/ItemClass';
import { Custom } from 'src/app/lib/@bus-tier/models/Custom';
import { AreaService } from 'src/app/services/area.service';
import { CommonWeaponsService } from 'src/app/services/commonWeaponsService';
import { ItemClassService } from 'src/app/services/item-class.service';
import { ItemService } from 'src/app/services/item.service';
import { ReviewService } from 'src/app/services/review.service';
import { CustomService } from 'src/app/services/custom.service';
import { Review } from 'src/lib/darkcloud';
import { ICommonWeapons } from 'src/lib/darkcloud/angular/dsadmin/v9/data/hard';
import { language } from 'src/lib/darkcloud/dialogue-system/game-types';

interface Itemlevel {
  id: string;
  nameHC: string;
  idNameHC?: string;
  typeItem: any;
  hc?: number;
  nameCircle?: string;
  idCircle?: string;
  receivedAt?: string[];
  valueRange?: string;
}

@Component({
  selector: 'app-common-weapons',
  templateUrl: './common-weapons.component.html',
  styleUrls: ['./common-weapons.component.scss']
})
export class CommonWeaponsComponent {

  language: language = 'PT-BR';
  listAreas: Area[];
  itemClasses: ItemClass[] = [];
  itemIds: string[];
  listItem:Itemlevel[];
  listCommon: Itemlevel[] = [];
  commonWeapons: ICommonWeapons [] = [];
  listCommonWeapons: CommonWeapons [] = [];
  description:string = '';
  title: string = 'Common Weapons';
  sortNameOrder = +1;
  valueRange: string;
  custom: Custom;

  constructor(
    private _commonWeaponsService: CommonWeaponsService,
    private _itemClassService: ItemClassService,
    private _areaService: AreaService,
    private _reviewService: ReviewService,
    private _itemService: ItemService,
    private _change: ChangeDetectorRef,
    private _customService: CustomService,
  ) { }
  
  public async ngOnInit() {

     this.listAreas = this._areaService.models;
     await this._customService.toFinishLoading();
     this.custom = await this._customService.svcGetInstance();

     // Use weaponsWLBaseClassItem to show data based on selected item classes
     // This allows dynamic filtering like app-powerup-information
     if (this.custom.weaponsWLBaseClassItem && this.custom.weaponsWLBaseClassItem.length > 0) {
       this.itemClasses = this._itemClassService.models.filter((klass) =>
         this.custom.weaponsWLBaseClassItem.includes(klass.id)
       );
     } else {
       // No item classes selected - show empty list
       this.itemClasses = [];
     }

     // Collect all item IDs from selected classes
     this.itemIds = [];
     this.itemClasses.forEach(itemClass => {
       if (itemClass && itemClass.itemIds) {
         this.itemIds = this.itemIds.concat(itemClass.itemIds);
       }
     });

     if (this.itemIds.length > 0) {
       this.ckeckCircleWeapon();
       this.processWeapons();
       this.addCommonWeapons();
       this._commonWeaponsService.toFinishLoading();
       this.listCommonWeapons = this._commonWeaponsService.models;
       this.lineupOrderCommon();
       this.updateDescription();
     }
  }

    ckeckCircleWeapon() {
 
      //Pega os dados das armas
      this.itemIds.forEach((item) => {
        const valueList = this._itemService.models.filter((op)=> item == op.id);
        this.listItem =  valueList.map((x) => ({id: x.id, nameHC: x.name, receivedAt: this.validatesItemInUse(x).receivedAt, typeItem: (x?.type != undefined ? x.type.toLocaleString() : x.type)})); 
         this.listItem.forEach((m) => {
          if(m.receivedAt.length > 0) this.listCommon.push(m)
        });       
      });

     //Verifica o nome do circulo
      this.listCommon.map((x) => {
        if(x.receivedAt.length > 0) {
          for (let index = 0; index < x.receivedAt.length; index++) {
            const IdCircle = Area.getSubIdFrom(x.receivedAt[index]);   
            const local = this._areaService.svcFindById(IdCircle);  
            x.nameCircle = local.name;
            x.idCircle = local.id ,
            x.hc = local.order;          
          }
        }
      });
    }

  //validates if item is Received
  validatesItemInUse(obj: any): Review.Result {
    if (!obj) return null;  
     let reviewsItem = this._reviewService.reviewResults[obj.id]; 
    return reviewsItem;
  } 

  addCommonWeapons() {
   
    const contains = this._commonWeaponsService.models;
    if(contains.length > 0) {
      this.commonWeapons.forEach((add) => {
        const matching  = contains.find(circle => circle.idCircle === add.idCircle)

        if(matching) {
          add.commonWeaponReceivedHC?.forEach((x) => {
            const matchingName =  matching.commonWeaponReceivedHC?.find( a => a.nameHc === x.nameHc);
            if(!matchingName) {
              this._commonWeaponsService.addNewCommonWeaponReceivedHC(add);
            }
          });

         // Remover Common Weapon se nameHc não existir em this.commonWeapons
            const commonWeaponToRemove = matching.commonWeaponReceivedHC?.filter(x =>
             !add.commonWeaponReceivedHC?.some(item => item?.nameHc === x?.nameHc)
           );
           
           if (commonWeaponToRemove && commonWeaponToRemove.length > 0) {
               commonWeaponToRemove.forEach(nameToRemove => {
               // Removendo os itens do commonWeaponReceivedHC que não estão mais presentes
                const indexToRemove = matching.commonWeaponReceivedHC?.findIndex(hcBP => hcBP.nameHc === nameToRemove.nameHc);
                if (indexToRemove !== undefined && indexToRemove !== -1) {
                    matching.commonWeaponReceivedHC?.splice(indexToRemove, 1);
                 }
               });
            }
        } else {
            // Se o idCircle não for encontrado, criar um novo                
             this._commonWeaponsService.createNewCommonWeapons(add);         
        }
      });       
        // Remover toda a estrutura de contains se o idCircle não existir em this.commonWeapons
        const itemsToRemove = contains.filter(hcItem =>
          !this.commonWeapons.some(item => item.idCircle === hcItem.idCircle)
      );

      if (itemsToRemove && itemsToRemove.length > 0) {
          itemsToRemove.forEach(itemToRemove => {
              this._commonWeaponsService.svcToRemove(itemToRemove.id);
          });
      }

    } else {
      this.commonWeapons.forEach((a) => {         
        this._commonWeaponsService.createNewCommonWeapons(a); 
       });
    }

    this._change.detectChanges();    
  }

  processWeapons() {
    const groupedWeapons: { [key: string]: Itemlevel[] } = {};

    // Agrupar os itens por nameCircle
    for (const item of this.listCommon) {
        if (item.nameCircle) {
            if (!groupedWeapons[item.nameCircle]) {
                groupedWeapons[item.nameCircle] = [];
            }
            groupedWeapons[item.nameCircle].push(item);
        }
    }
    
    // Criar a estrutura de commonWeapons
    for (const [nameCircle, items] of Object.entries(groupedWeapons)) {
        if (items.length > 0) {
            const commonWeapon: ICommonWeapons = {
                id: undefined,
                name: nameCircle,
                idCircle: items[0].idCircle,              
                hc: items[0].hc ? items[0].hc : undefined,
                commonWeaponReceivedHC: items.map(item => ({
                    nameHc: item.nameHC,
                    idNameHC: item.id,               
                })),
            };
            this.commonWeapons.push(commonWeapon);
        }
    }
}

getWlRangeClass(hc: number): string {
  const roundedHc = Math.floor(hc); 

  if (roundedHc >= 0 && roundedHc <= 2) {
    this.valueRange = "1 - 6";  
      return 'green-bg'; 
  } else if (roundedHc >= 3 && roundedHc <= 5) {
    this.valueRange = "7 - 12"; 
      return 'primary-bg'; 
  } else if (roundedHc >= 6 && roundedHc <= 7) {
    this.valueRange = "13 - 16";
      return 'purple-bg'; 
  } else if (roundedHc >= 8 && roundedHc <= 9) {
    this.valueRange = "17 - 20";
      return 'yellow-bg'; 
  } else {
    this.valueRange = '';
    return '';
  }

}

updateDescription() {
  // Count total received results (sum of all commonWeaponReceivedHC arrays)
  let totalResults = 0;
  this.listCommonWeapons.forEach(weapon => {
    if (weapon.commonWeaponReceivedHC) {
      totalResults += weapon.commonWeaponReceivedHC.length;
    }
  });
  this.description = `Showing ${totalResults} received results`;
}

onChangeCommonWeapons(comm: CommonWeapons, value: string, fieldName:string, index) {

  if(fieldName === 'qi_Min') comm.commonWeaponReceivedHC[index].qi_Min = value == '' ? undefined : +value;
  if(fieldName === 'luck_min') comm.commonWeaponReceivedHC[index].luck_min = value == '' ? undefined : +value; 
  if(fieldName === 'wlBase') comm.commonWeaponReceivedHC[index].wlBase = value == '' ? undefined : +value; 

  this._commonWeaponsService.svcToModify(comm);
  this._commonWeaponsService.toSave(); 
}

initSortNameOrderCommon: number = +1

lineupOrderCommon() 
  {
  this.sortNameOrder *= +1;
    this.listCommonWeapons.sort((a, b) => 
    {  
      return this.sortNameOrder * a.name.localeCompare(b.name);
    });
  }

  sortNameOrderCommon: number = +1; 
  orderSortCommon() {
      this.sortNameOrderCommon *= -1;
       this.listCommonWeapons.forEach(comm => {
          comm.commonWeaponReceivedHC.sort((a, b) => {
           return this.sortNameOrderCommon * a.nameHc.localeCompare(b.nameHc)
       });
      });  
      this._change.detectChanges();
  }
  
  sortNameOrderHC = 1; 

  orderSortCommonHC() {
    this.sortNameOrderHC *= -1; // Alterna entre 1 e -1 para ordenação crescente e decrescente
    
    this.listCommonWeapons.sort((a, b) => {
      return this.sortNameOrderHC * (a.hc - b.hc);
    });
  }


} 
